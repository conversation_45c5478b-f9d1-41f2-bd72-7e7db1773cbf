import {
  SimpleInterestInput,
  CompoundInterestInput,
  RegularInvestmentInput,
  SimpleInterestResult,
  CompoundInterestResult,
  RegularInvestmentResult,
  InvestmentTimePoint,
  CompoundFrequency,
  RegularFrequency
} from '../types/investment';

/**
 * 計算單利投資
 * @param input 單利投資輸入參數
 * @returns 單利投資結果
 */
export function calculateSimpleInterest(input: SimpleInterestInput): SimpleInterestResult {
  const { principal, rate, rateType, period, periodType } = input;

  // 統一轉換為年利率和年期
  let annualRate = rate;
  let years = period;

  if (rateType === 'monthly') {
    annualRate = rate * 12;
  }

  if (periodType === 'months') {
    years = period / 12;
  }

  // 單利公式: A = P(1 + rt)
  const totalInterest = principal * (annualRate / 100) * years;
  const finalAmount = principal + totalInterest;
  const returnRate = (totalInterest / principal) * 100;
  const annualizedReturn = returnRate / years;

  // 生成時間點數據
  const timePoints: InvestmentTimePoint[] = [];
  const totalPeriods = periodType === 'years' ? Math.ceil(years) : period;

  for (let i = 0; i <= totalPeriods; i++) {
    const currentYears = periodType === 'years' ? i : i / 12;
    const currentInterest = principal * (annualRate / 100) * currentYears;
    const currentTotal = principal + currentInterest;

    timePoints.push({
      period: i,
      principal: principal,
      interest: currentInterest,
      total: currentTotal,
      periodInterest: i > 0 ? principal * (annualRate / 100) * (periodType === 'years' ? 1 : 1 / 12) : 0
    });
  }

  return {
    finalAmount,
    totalInterest,
    principal,
    returnRate,
    annualizedReturn,
    timePoints
  };
}

/**
 * 計算複利投資
 * @param input 複利投資輸入參數
 * @returns 複利投資結果
 */
export function calculateCompoundInterest(input: CompoundInterestInput): CompoundInterestResult {
  const { principal, rate, years, compoundFrequency } = input;

  // 確定複利次數
  let n: number;
  switch (compoundFrequency) {
    case 'annually':
      n = 1;
      break;
    case 'monthly':
      n = 12;
      break;
    case 'daily':
      n = 365;
      break;
    default:
      n = 12;
  }

  // 複利公式: A = P(1 + r/n)^(nt)
  const finalAmount = principal * Math.pow(1 + rate / 100 / n, n * years);
  const totalInterest = finalAmount - principal;
  const returnRate = (totalInterest / principal) * 100;
  const annualizedReturn = returnRate / years;

  // 計算同條件下的單利結果
  const simpleInterest = principal * (rate / 100) * years;
  const compoundEffect = totalInterest - simpleInterest;
  const compoundAdvantage = simpleInterest > 0 ? (compoundEffect / simpleInterest) * 100 : 0;

  // 生成時間點數據
  const timePoints: InvestmentTimePoint[] = [];
  const periodsPerYear = compoundFrequency === 'monthly' ? 12 : compoundFrequency === 'daily' ? 365 : 1;
  const totalPeriods = Math.ceil(years * periodsPerYear);

  for (let i = 0; i <= totalPeriods; i++) {
    const currentYears = i / periodsPerYear;
    const currentAmount = principal * Math.pow(1 + rate / 100 / n, n * currentYears);
    const currentInterest = currentAmount - principal;

    timePoints.push({
      period: i,
      principal: principal,
      interest: currentInterest,
      total: currentAmount,
      periodInterest: i > 0 ? currentAmount - timePoints[i - 1].total : 0
    });
  }

  return {
    finalAmount,
    totalInterest,
    principal,
    returnRate,
    annualizedReturn,
    compoundEffect,
    timePoints,
    comparison: {
      simpleInterest: principal + simpleInterest,
      compoundAdvantage
    }
  };
}

/**
 * 計算定期定額投資
 * @param input 定期定額投資輸入參數
 * @returns 定期定額投資結果
 */
export function calculateRegularInvestment(input: RegularInvestmentInput): RegularInvestmentResult {
  const { initialAmount, regularAmount, rate, years, frequency } = input;

  // 確定投入頻率
  let periodsPerYear: number;
  switch (frequency) {
    case 'monthly':
      periodsPerYear = 12;
      break;
    case 'quarterly':
      periodsPerYear = 4;
      break;
    case 'annually':
      periodsPerYear = 1;
      break;
    default:
      periodsPerYear = 12;
  }

  const totalPeriods = years * periodsPerYear;
  const periodRate = rate / 100 / periodsPerYear;

  // 計算未來價值
  let currentAmount = initialAmount;
  const timePoints: InvestmentTimePoint[] = [];
  let totalInvestment = initialAmount;

  // 初始狀態
  timePoints.push({
    period: 0,
    principal: initialAmount,
    interest: 0,
    total: initialAmount,
    periodInterest: 0,
    periodContribution: initialAmount
  });

  for (let period = 1; period <= totalPeriods; period++) {
    // 計算利息
    const periodInterest = currentAmount * periodRate;

    // 加入定期投入
    currentAmount = currentAmount + periodInterest + regularAmount;
    totalInvestment += regularAmount;

    const totalInterest = currentAmount - totalInvestment;

    timePoints.push({
      period,
      principal: totalInvestment,
      interest: totalInterest,
      total: currentAmount,
      periodInterest,
      periodContribution: regularAmount
    });
  }

  const finalAmount = currentAmount;
  const totalReturn = finalAmount - totalInvestment;
  const returnRate = (totalReturn / totalInvestment) * 100;
  const annualizedReturn = returnRate / years;

  // 計算平均餘額
  const averageBalance = timePoints.reduce((sum, point) => sum + point.total, 0) / timePoints.length;

  // 計算有效利率
  const effectiveRate = totalInvestment > 0 ? (totalReturn / totalInvestment / years) * 100 : 0;

  return {
    totalInvestment,
    finalAmount,
    totalReturn,
    returnRate,
    annualizedReturn,
    timePoints,
    summary: {
      totalPeriods,
      averageBalance,
      effectiveRate
    }
  };
}

/**
 * 驗證單利投資輸入參數
 * @param input 單利投資輸入參數
 * @returns 驗證錯誤訊息
 */
export function validateSimpleInterestInput(input: Partial<SimpleInterestInput>): string[] {
  const errors: string[] = [];

  if (!input.principal || input.principal <= 0) {
    errors.push('本金必須大於 0');
  }

  if (!input.rate || input.rate <= 0 || input.rate > 100) {
    errors.push('利率必須在 0-100% 之間');
  }

  if (!input.period || input.period <= 0) {
    errors.push('投資期間必須大於 0');
  }

  if (input.periodType === 'months' && input.period && input.period > 600) {
    errors.push('投資期間不能超過 600 個月');
  }

  if (input.periodType === 'years' && input.period && input.period > 50) {
    errors.push('投資期間不能超過 50 年');
  }

  return errors;
}

/**
 * 驗證複利投資輸入參數
 * @param input 複利投資輸入參數
 * @returns 驗證錯誤訊息
 */
export function validateCompoundInterestInput(input: Partial<CompoundInterestInput>): string[] {
  const errors: string[] = [];

  if (!input.principal || input.principal <= 0) {
    errors.push('初始本金必須大於 0');
  }

  if (!input.rate || input.rate <= 0 || input.rate > 100) {
    errors.push('年利率必須在 0-100% 之間');
  }

  if (!input.years || input.years <= 0 || input.years > 50) {
    errors.push('投資年期必須在 1-50 年之間');
  }

  return errors;
}

/**
 * 驗證定期定額投資輸入參數
 * @param input 定期定額投資輸入參數
 * @returns 驗證錯誤訊息
 */
export function validateRegularInvestmentInput(input: Partial<RegularInvestmentInput>): string[] {
  const errors: string[] = [];

  if (input.initialAmount !== undefined && input.initialAmount < 0) {
    errors.push('初始本金不能為負數');
  }

  if (!input.regularAmount || input.regularAmount <= 0) {
    errors.push('每期投入金額必須大於 0');
  }

  if (!input.rate || input.rate <= 0 || input.rate > 100) {
    errors.push('年利率必須在 0-100% 之間');
  }

  if (!input.years || input.years <= 0 || input.years > 50) {
    errors.push('投資年期必須在 1-50 年之間');
  }

  return errors;
}

/**
 * 計算投資建議
 * @param rate 年利率
 * @param years 投資年期
 * @param amount 投資金額
 * @returns 投資建議
 */
export function getInvestmentAdvice(rate: number, years: number, amount: number) {
  const advice = {
    riskLevel: 'medium' as 'low' | 'medium' | 'high',
    recommendation: '',
    tips: [] as string[],
    warnings: [] as string[]
  };

  // 根據利率判斷風險等級
  if (rate < 3) {
    advice.riskLevel = 'low';
    advice.recommendation = '低風險穩健投資，適合保守型投資者';
    advice.tips.push('考慮定存、政府公債等低風險商品');
    advice.tips.push('雖然報酬率較低，但本金安全性高');
  } else if (rate < 8) {
    advice.riskLevel = 'medium';
    advice.recommendation = '中等風險投資，適合穩健型投資者';
    advice.tips.push('可考慮平衡型基金、公司債等');
    advice.tips.push('建議分散投資降低風險');
  } else {
    advice.riskLevel = 'high';
    advice.recommendation = '高風險高報酬投資，適合積極型投資者';
    advice.tips.push('股票、股票型基金等高風險商品');
    advice.warnings?.push('高報酬伴隨高風險，可能面臨本金損失');
    advice.warnings?.push('建議只投入可承受損失的資金');
  }

  // 根據投資期間給建議
  if (years >= 10) {
    advice.tips.push('長期投資有助於平滑市場波動');
    advice.tips.push('複利效應在長期投資中更為顯著');
  } else if (years < 3) {
    advice.warnings?.push('短期投資建議選擇較穩健的商品');
  }

  // 根據投資金額給建議
  if (amount >= 1000000) {
    advice.tips.push('大額投資建議分批進場，降低時點風險');
    advice.tips.push('可考慮專業理財顧問服務');
  }

  return advice;
}
