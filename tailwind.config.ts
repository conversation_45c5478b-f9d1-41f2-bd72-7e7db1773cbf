import type { Config } from 'tailwindcss'

const config: Config = {
    content: [
        './app/**/*.{js,ts,jsx,tsx}',       // App Router
        './pages/**/*.{js,ts,jsx,tsx}',     // Pages Router
        './components/**/*.{js,ts,jsx,tsx}' // 你自己的元件目錄
    ],
    darkMode: false, // or 'media' or 'class'
    theme: {
        extend: {
            colors: {
                // 可選：你可將 CSS 變數綁進 theme
                background: 'var(--background)',
                foreground: 'var(--foreground)',
            },
            fontFamily: {
                // 例如你有定義 Geist 字體
                sans: ['var(--font-geist-sans)', 'sans-serif'],
                mono: ['var(--font-geist-mono)', 'monospace'],
            },
        },
    },
    plugins: [],
}

export default config
