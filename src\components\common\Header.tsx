'use client';

import { Menubar } from 'primereact/menubar';
import { MenuItem } from 'primereact/menuitem';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function Header() {
  const router = useRouter();

  const items: MenuItem[] = [
    {
      label: '信貸計算',
      icon: 'pi pi-calculator',
      command: () => router.push('/credit-loan')
    },
    {
      label: '房貸計算',
      icon: 'pi pi-building',
      command: () => router.push('/mortgage')
    },
    {
      label: '投資回報',
      icon: 'pi pi-chart-line',
      command: () => router.push('/investment')
    }
  ];

  const start = (
    <div className="flex items-center gap-2">
      <Link href="/">
        <i className="pi pi-calculator text-2xl text-blue-600"></i>
        <span className="font-bold text-xl text-gray-800">貸款計算機</span>
      </Link>
    </div>
  );

  // const end = (
  //   <Button
  //     icon="pi pi-cog"
  //     className="p-button-text"
  //     tooltip="設定"
  //     tooltipOptions={{ position: 'bottom' }}
  //   />
  // );

  return (
    <div className="bg-white shadow-sm border-none">
      <Menubar
        model={items}
        start={start}
        // end={end}
        className="border-none bg-transparent"
      />
    </div>
  );
}